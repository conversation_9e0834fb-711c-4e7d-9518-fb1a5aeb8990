<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.hanyi.compat</groupId>
        <artifactId>project-compat</artifactId>
        <version>${compat.version}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>


    <artifactId>bochk-compat</artifactId>
    <version>1.12.2.101-BOCHK</version>
    <name>bochk-compat</name>

    <properties>
        <core.version>1.11.4.100-BOCHK</core.version>
        <survey.version>1.11.4.105-BOCHK</survey.version>
        <ctm.version>1.11.4.103-BOCHK</ctm.version>
        <maven.compiler.source>14</maven.compiler.source>
        <maven.compiler.target>14</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb1-impl</artifactId>
            <version>2.2.3-1</version>
        </dependency>

        <!-- JAXB Core -->
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>2.3.0.1</version>
        </dependency>

        <!-- Activation API (JAXB 需要) -->
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>2.3.3</version> <!-- Use the appropriate version -->
        </dependency>

        <dependency>
            <groupId>com.sun.msv</groupId>
            <artifactId>com.springsource.com.sun.msv.grammar</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.ibm.icu</groupId>
            <artifactId>icu4j</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.ibm.icu</groupId>
            <artifactId>icu4j-charset</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.ibm.mcf</groupId>
            <artifactId>MCF_COMMON</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.befun</groupId>
            <artifactId>befun-core</artifactId>
            <version>${core.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>survey-core</artifactId>
            <version>${survey.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>survey-compat</artifactId>
            <version>${survey.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>survey-base</artifactId>
            <version>${survey.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-data-core</artifactId>
            <version>${ctm.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
        </dependency>
    </dependencies>

</project>